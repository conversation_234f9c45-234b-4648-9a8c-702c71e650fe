import { createClient, RealtimeChannel } from '@supabase/supabase-js';
import type { Database } from './types';
import { config } from '@/lib/config';

const supabaseUrl = config.supabase.url;
const supabaseAnonKey = config.supabase.anonKey;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});

// Database helper functions
export const db = {
  // Profile operations (replaces users)
  profiles: {
    async getById(userId: string) {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      return data;
    },

    async updateProfile(userId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
  },

  // Project operations
  projects: {
    async getAll(userId: string) {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          chapters(count)
        `)
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async getById(projectId: string) {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          chapters(
            id,
            chapter_number,
            title,
            actual_word_count,
            status
          )
        `)
        .eq('id', projectId)
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(projectData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(projectId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', projectId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(projectId: string) {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);
      
      if (error) throw error;
    },

    async updateWordCount(projectId: string) {
      // Calculate total word count from chapters
      const { data: chapters, error: chaptersError } = await supabase
        .from('chapters')
        .select('actual_word_count')
        .eq('project_id', projectId);
      
      if (chaptersError) throw chaptersError;
      
      const totalWords = chapters.reduce((sum, ch) => sum + (ch.actual_word_count || 0), 0);
      
      const { error } = await supabase
        .from('projects')
        .update({ 
          current_word_count: totalWords
        })
        .eq('id', projectId);
      
      if (error) throw error;
    },
  },

  // Chapter operations
  chapters: {
    async getAll(projectId: string) {
      const { data, error } = await supabase
        .from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true });
      
      if (error) throw error;
      return data;
    },

    async getById(chapterId: string) {
      const { data, error } = await supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(chapterData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('chapters')
        .insert(chapterData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(chapterId: string, updates: Record<string, unknown>) {
      // Update word count if content is provided
      if (updates.content) {
        updates.actual_word_count = String(updates.content).split(/\s+/).filter(Boolean).length;
      }

      const { data, error } = await supabase
        .from('chapters')
        .update(updates)
        .eq('id', chapterId)
        .select()
        .single();
      
      if (error) throw error;
      
      // Update project word count
      if (data && updates.content) {
        await db.projects.updateWordCount(data.project_id);
      }
      
      return data;
    },

    async delete(chapterId: string) {
      const { data, error } = await supabase
        .from('chapters')
        .select('project_id')
        .eq('id', chapterId)
        .single();
      
      if (error) throw error;
      
      const { error: deleteError } = await supabase
        .from('chapters')
        .delete()
        .eq('id', chapterId);
      
      if (deleteError) throw deleteError;
      
      // Update project word count
      if (data) {
        await db.projects.updateWordCount(data.project_id);
      }
    },
  },

  // Character operations
  characters: {
    async getAll(projectId: string) {
      const { data, error } = await supabase
        .from('characters')
        .select('*')
        .eq('project_id', projectId)
        .order('name', { ascending: true });
      
      if (error) throw error;
      return data;
    },

    async create(characterData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('characters')
        .insert(characterData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(characterId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('characters')
        .update(updates)
        .eq('id', characterId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
  },

  // Story Bible operations
  storyBible: {
    async getAll(projectId: string) {
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .order('category', { ascending: true });
      
      if (error) throw error;
      return data;
    },

    async create(entryData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('story_bible')
        .insert(entryData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(entryId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('story_bible')
        .update(updates)
        .eq('id', entryId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
  },

  // AI Processing operations
  processing: {
    async createTask(taskData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('processing_tasks')
        .insert(taskData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async updateTask(taskId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('processing_tasks')
        .update(updates)
        .eq('id', taskId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async getProjectTasks(projectId: string) {
      const { data, error } = await supabase
        .from('processing_tasks')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  },

  // Selection Profile operations
  selectionProfiles: {
    async getPublic() {
      const { data, error } = await supabase
        .from('selection_profiles')
        .select('*')
        .eq('is_public', true)
        .order('usage_count', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async getUserProfiles(userId: string) {
      const { data, error } = await supabase
        .from('selection_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async create(profileData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('selection_profiles')
        .insert(profileData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async incrementUsage(profileId: string) {
      const { error } = await supabase.rpc('increment_profile_usage', {
        profile_id: profileId
      });
      
      if (error) throw error;
    },
  },

  // Reference Materials operations
  references: {
    async getAll(projectId: string) {
      const { data, error } = await supabase
        .from('reference_materials')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async create(referenceData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('reference_materials')
        .insert(referenceData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(referenceId: string) {
      const { error } = await supabase
        .from('reference_materials')
        .delete()
        .eq('id', referenceId);
      
      if (error) throw error;
    },
  },

  // Writing session operations
  sessions: {
    async create(sessionData: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('writing_sessions')
        .insert(sessionData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async end(sessionId: string, stats: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('writing_sessions')
        .update({
          ended_at: new Date().toISOString(),
          ...stats
        })
        .eq('id', sessionId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async getUserStats(userId: string, days: number = 30) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const { data, error } = await supabase
        .from('writing_sessions')
        .select('*')
        .eq('user_id', userId)
        .gte('started_at', startDate.toISOString())
        .order('started_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  },

  // Timeline Events operations (using story_bible table with timeline_event type)
  timelineEvents: {
    async getAll(projectId: string) {
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .eq('entry_type', 'timeline_event')
        .order('chapter_introduced', { ascending: true });
      
      if (error) throw error;
      return data;
    },

    async getById(eventId: string) {
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('id', eventId)
        .eq('entry_type', 'timeline_event')
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(eventData: Record<string, unknown>) {
      const storyBibleEntry = {
        ...eventData,
        entry_type: 'timeline_event'
      };
      
      const { data, error } = await supabase
        .from('story_bible')
        .insert(storyBibleEntry)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(eventId: string, updates: Record<string, unknown>) {
      const { data, error } = await supabase
        .from('story_bible')
        .update(updates)
        .eq('id', eventId)
        .eq('entry_type', 'timeline_event')
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(eventId: string) {
      const { error } = await supabase
        .from('story_bible')
        .delete()
        .eq('id', eventId)
        .eq('entry_type', 'timeline_event');
      
      if (error) throw error;
    },

    async getByChapter(projectId: string, chapterNumber: number) {
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .eq('entry_type', 'timeline_event')
        .eq('chapter_introduced', chapterNumber)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data;
    },
  },

  // Content Embeddings operations
  embeddings: {
    async create(embeddingData: {
      project_id: string;
      content_type: string;
      content_id: string;
      text_content: string;
      metadata?: Record<string, unknown>;
    }) {
      try {
        // Import here to avoid circular dependencies
        const { generateEmbedding } = await import('../openai');
        
        // Generate embedding using OpenAI
        const embedding = await generateEmbedding(embeddingData.text_content);
        
        // Insert into database
        const { data, error } = await supabase
          .from('content_embeddings')
          .insert({
            project_id: embeddingData.project_id,
            content_type: embeddingData.content_type,
            content_id: embeddingData.content_id,
            text_content: embeddingData.text_content,
            embedding,
            metadata: embeddingData.metadata || null,
          })
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error creating content embedding:', error);
        throw new Error(`Failed to create content embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async getByProject(projectId: string, contentType?: string) {
      try {
        let query = supabase
          .from('content_embeddings')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false });
        
        if (contentType) {
          query = query.eq('content_type', contentType);
        }
        
        const { data, error } = await query;
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching content embeddings:', error);
        throw new Error(`Failed to fetch content embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async search(
      projectId: string,
      queryText: string,
      options: {
        contentType?: string;
        similarityThreshold?: number;
        matchCount?: number;
      } = {}
    ) {
      try {
        // Import here to avoid circular dependencies
        const { generateEmbedding } = await import('../openai');
        
        // Generate embedding for the query
        const queryEmbedding = await generateEmbedding(queryText);
        
        // Use the PostgreSQL function for similarity search
        const { data, error } = await supabase.rpc('search_similar_content', {
          query_embedding: queryEmbedding,
          project_uuid: projectId,
          content_type_filter: options.contentType || null,
          similarity_threshold: options.similarityThreshold || 0.7,
          match_count: options.matchCount || 10,
        });
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error searching content embeddings:', error);
        throw new Error(`Failed to search content embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async searchByEmbedding(
      projectId: string,
      queryEmbedding: number[],
      options: {
        contentType?: string;
        similarityThreshold?: number;
        matchCount?: number;
      } = {}
    ) {
      try {
        // Use the PostgreSQL function for similarity search
        const { data, error } = await supabase.rpc('search_similar_content', {
          query_embedding: queryEmbedding,
          project_uuid: projectId,
          content_type_filter: options.contentType || null,
          similarity_threshold: options.similarityThreshold || 0.7,
          match_count: options.matchCount || 10,
        });
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error searching content embeddings by embedding:', error);
        throw new Error(`Failed to search content embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async update(embeddingId: string, updates: {
      text_content?: string;
      metadata?: Record<string, unknown>;
    }) {
      try {
        let updateData: Record<string, unknown> = {};
        
        // If text content is being updated, regenerate the embedding
        if (updates.text_content) {
          const { generateEmbedding } = await import('../openai');
          const embedding = await generateEmbedding(updates.text_content);
          updateData = {
            text_content: updates.text_content,
            embedding,
            metadata: updates.metadata || null,
          };
        } else if (updates.metadata) {
          updateData = {
            metadata: updates.metadata,
          };
        }
        
        const { data, error } = await supabase
          .from('content_embeddings')
          .update(updateData)
          .eq('id', embeddingId)
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error updating content embedding:', error);
        throw new Error(`Failed to update content embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async delete(embeddingId: string) {
      try {
        const { error } = await supabase
          .from('content_embeddings')
          .delete()
          .eq('id', embeddingId);
        
        if (error) throw error;
      } catch (error) {
        console.error('Error deleting content embedding:', error);
        throw new Error(`Failed to delete content embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async deleteByProject(projectId: string) {
      try {
        const { error } = await supabase
          .from('content_embeddings')
          .delete()
          .eq('project_id', projectId);
        
        if (error) throw error;
      } catch (error) {
        console.error('Error deleting project embeddings:', error);
        throw new Error(`Failed to delete project embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async deleteByContent(projectId: string, contentType: string, contentId: string) {
      try {
        const { error } = await supabase
          .from('content_embeddings')
          .delete()
          .eq('project_id', projectId)
          .eq('content_type', contentType)
          .eq('content_id', contentId);
        
        if (error) throw error;
      } catch (error) {
        console.error('Error deleting content embeddings:', error);
        throw new Error(`Failed to delete content embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async getById(embeddingId: string) {
      try {
        const { data, error } = await supabase
          .from('content_embeddings')
          .select('*')
          .eq('id', embeddingId)
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error fetching content embedding:', error);
        throw new Error(`Failed to fetch content embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },

    async createBatch(embeddingDataArray: Array<{
      project_id: string;
      content_type: string;
      content_id: string;
      text_content: string;
      metadata?: Record<string, unknown>;
    }>) {
      try {
        if (!embeddingDataArray || embeddingDataArray.length === 0) {
          throw new Error('Embedding data array cannot be empty');
        }

        // Import here to avoid circular dependencies
        const { generateEmbeddings } = await import('../openai');
        
        // Extract texts for batch embedding generation
        const texts = embeddingDataArray.map(item => item.text_content);
        
        // Generate embeddings in batch
        const embeddings = await generateEmbeddings(texts);
        
        // Prepare data for batch insert
        const insertData = embeddingDataArray.map((item, index) => ({
          project_id: item.project_id,
          content_type: item.content_type,
          content_id: item.content_id,
          text_content: item.text_content,
          embedding: embeddings[index],
          metadata: item.metadata || null,
        }));
        
        // Insert all embeddings
        const { data, error } = await supabase
          .from('content_embeddings')
          .insert(insertData)
          .select();
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error creating batch content embeddings:', error);
        throw new Error(`Failed to create batch content embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    },
  },
};

// Real-time subscriptions
export const subscriptions = {
  subscribeToProject(projectId: string, callback: (payload: Record<string, unknown>) => void) {
    return supabase
      .channel(`project:${projectId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'projects',
          filter: `id=eq.${projectId}`
        }, 
        callback
      )
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `project_id=eq.${projectId}`
        },
        callback
      )
      .subscribe();
  },

  subscribeToProcessingTasks(projectId: string, callback: (payload: Record<string, unknown>) => void) {
    return supabase
      .channel(`processing:${projectId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'processing_tasks',
          filter: `project_id=eq.${projectId}`
        },
        callback
      )
      .subscribe();
  },

  unsubscribe(subscription: RealtimeChannel) {
    supabase.removeChannel(subscription);
  },
};