import { createBrowserClient } from '@supabase/ssr'
import { createClient as createServerClient } from '@/lib/supabase/server'
import type { Database } from './types'
import { config } from '@/lib/config'

// For client components
export function createTypedBrowserClient() {
  const supabaseUrl = config.supabase.url
  const supabaseAnonKey = config.supabase.anonKey

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting
      },
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'x-application-name': 'bookscribe',
      },
    },
  })
}

// For server components and API routes
export async function createTypedServerClient() {
  const client = await createServerClient()
  return client
}

// Export a singleton for client-side usage
let browserClient: ReturnType<typeof createTypedBrowserClient> | null = null

export function getTypedBrowserClient() {
  if (!browserClient && typeof window !== 'undefined') {
    browserClient = createTypedBrowserClient()
  }
  return browserClient
}