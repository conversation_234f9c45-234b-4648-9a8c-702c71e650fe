'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import { config } from '@/lib/config'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error monitoring service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle>Something went wrong!</CardTitle>
          <CardDescription>
            Don't worry - your work has been saved. You can try again or return to your dashboard.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-muted rounded-lg space-y-2">
            <p className="text-sm font-medium">What you can try:</p>
            <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
              <li>Refresh the page to try again</li>
              <li>Check your internet connection</li>
              <li>Clear your browser cache</li>
              <li>Try using a different browser</li>
            </ul>
          </div>
          
          {config.isDevelopment && (
            <details className="p-3 bg-gray-100 rounded-md">
              <summary className="text-xs text-gray-600 cursor-pointer">Technical details</summary>
              <p className="text-xs text-gray-600 font-mono break-all mt-2">
                {error.message}
              </p>
              {error.digest && (
                <p className="text-xs text-gray-500 mt-2">
                  Error ID: {error.digest}
                </p>
              )}
            </details>
          )}
          
          <div className="flex gap-2">
            <Button 
              onClick={reset}
              className="flex-1"
              variant="outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          </div>
          
          <p className="text-sm text-muted-foreground text-center">
            If this problem persists, please contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </CardContent>
      </Card>
    </div>
  )
}